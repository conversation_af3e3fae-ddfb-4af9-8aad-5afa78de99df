// Test-only module that doesn't depend on NAPI
#[cfg(test)]
pub mod test_utils {
    use crate::types::ParsedEntry;
    use crate::utils::{convert_entries_to_parsed, convert_parsed_entry_to_solana_entry};
    use solana_entry::entry::Entry as SolanaEntry;
    use std::fs;
    use std::path::Path;

    pub fn load_test_shred(filename: &str) -> Vec<u8> {
        let path = Path::new("tests/data").join(filename);
        fs::read(path).expect(&format!("Failed to read test file: {}", filename))
    }

    pub fn deserialize_with_solana_crates(bytes: &[u8]) -> Vec<SolanaEntry> {
        bincode::deserialize(bytes).expect("Failed to deserialize with Solana crates")
    }

    pub fn deserialize_with_our_decoder(bytes: &[u8]) -> ParsedEntry {
        let entries: Vec<SolanaEntry> = bincode::deserialize(bytes)
            .expect("Failed to deserialize with bincode");
        convert_entries_to_parsed(entries)
    }

    pub fn compare_hashes(original: &solana_hash::Hash, converted: &solana_hash::Hash) -> bool {
        original.to_bytes() == converted.to_bytes()
    }

    pub fn compare_pubkeys(original: &solana_pubkey::Pubkey, converted: &solana_pubkey::Pubkey) -> bool {
        original.to_bytes() == converted.to_bytes()
    }

    pub fn compare_signatures(original: &solana_signature::Signature, converted: &solana_signature::Signature) -> bool {
        original.as_ref() == converted.as_ref()
    }

    pub fn compare_message_headers(
        original: &solana_message::MessageHeader,
        converted: &solana_message::MessageHeader,
    ) -> bool {
        original.num_required_signatures == converted.num_required_signatures
            && original.num_readonly_signed_accounts == converted.num_readonly_signed_accounts
            && original.num_readonly_unsigned_accounts == converted.num_readonly_unsigned_accounts
    }

    pub fn compare_compiled_instructions(
        original: &solana_message::compiled_instruction::CompiledInstruction,
        converted: &solana_message::compiled_instruction::CompiledInstruction,
    ) -> bool {
        original.program_id_index == converted.program_id_index
            && original.accounts == converted.accounts
            && original.data == converted.data
    }

    pub fn compare_address_table_lookups(
        original: &solana_message::v0::MessageAddressTableLookup,
        converted: &solana_message::v0::MessageAddressTableLookup,
    ) -> bool {
        compare_pubkeys(&original.account_key, &converted.account_key)
            && original.writable_indexes == converted.writable_indexes
            && original.readonly_indexes == converted.readonly_indexes
    }

    pub fn compare_legacy_messages(
        original: &solana_message::Message,
        converted: &solana_message::Message,
    ) -> bool {
        if !compare_message_headers(&original.header, &converted.header) {
            return false;
        }
        
        if original.account_keys.len() != converted.account_keys.len() {
            return false;
        }
        
        for (orig_key, conv_key) in original.account_keys.iter().zip(converted.account_keys.iter()) {
            if !compare_pubkeys(orig_key, conv_key) {
                return false;
            }
        }
        
        if !compare_hashes(&original.recent_blockhash, &converted.recent_blockhash) {
            return false;
        }
        
        if original.instructions.len() != converted.instructions.len() {
            return false;
        }
        
        for (orig_inst, conv_inst) in original.instructions.iter().zip(converted.instructions.iter()) {
            if !compare_compiled_instructions(orig_inst, conv_inst) {
                return false;
            }
        }
        
        true
    }

    pub fn compare_v0_messages(
        original: &solana_message::v0::Message,
        converted: &solana_message::v0::Message,
    ) -> bool {
        if !compare_message_headers(&original.header, &converted.header) {
            return false;
        }
        
        if original.account_keys.len() != converted.account_keys.len() {
            return false;
        }
        
        for (orig_key, conv_key) in original.account_keys.iter().zip(converted.account_keys.iter()) {
            if !compare_pubkeys(orig_key, conv_key) {
                return false;
            }
        }
        
        if !compare_hashes(&original.recent_blockhash, &converted.recent_blockhash) {
            return false;
        }
        
        if original.instructions.len() != converted.instructions.len() {
            return false;
        }
        
        for (orig_inst, conv_inst) in original.instructions.iter().zip(converted.instructions.iter()) {
            if !compare_compiled_instructions(orig_inst, conv_inst) {
                return false;
            }
        }
        
        if original.address_table_lookups.len() != converted.address_table_lookups.len() {
            return false;
        }
        
        for (orig_lookup, conv_lookup) in original.address_table_lookups.iter().zip(converted.address_table_lookups.iter()) {
            if !compare_address_table_lookups(orig_lookup, conv_lookup) {
                return false;
            }
        }
        
        true
    }

    pub fn compare_versioned_messages(
        original: &solana_message::VersionedMessage,
        converted: &solana_message::VersionedMessage,
    ) -> bool {
        match (original, converted) {
            (solana_message::VersionedMessage::Legacy(orig), solana_message::VersionedMessage::Legacy(conv)) => {
                compare_legacy_messages(orig, conv)
            }
            (solana_message::VersionedMessage::V0(orig), solana_message::VersionedMessage::V0(conv)) => {
                compare_v0_messages(orig, conv)
            }
            _ => false, // Different message types
        }
    }

    pub fn compare_versioned_transactions(
        original: &solana_transaction::versioned::VersionedTransaction,
        converted: &solana_transaction::versioned::VersionedTransaction,
    ) -> bool {
        if original.signatures.len() != converted.signatures.len() {
            return false;
        }
        
        for (orig_sig, conv_sig) in original.signatures.iter().zip(converted.signatures.iter()) {
            if !compare_signatures(orig_sig, conv_sig) {
                return false;
            }
        }
        
        compare_versioned_messages(&original.message, &converted.message)
    }

    pub fn compare_entries(original: &SolanaEntry, converted: &SolanaEntry) -> bool {
        if original.num_hashes != converted.num_hashes {
            return false;
        }
        
        if !compare_hashes(&original.hash, &converted.hash) {
            return false;
        }
        
        if original.transactions.len() != converted.transactions.len() {
            return false;
        }
        
        for (orig_tx, conv_tx) in original.transactions.iter().zip(converted.transactions.iter()) {
            if !compare_versioned_transactions(orig_tx, conv_tx) {
                return false;
            }
        }
        
        true
    }

    pub fn compare_entry_vectors(original: &[SolanaEntry], converted: &[SolanaEntry]) -> bool {
        if original.len() != converted.len() {
            return false;
        }
        
        for (orig_entry, conv_entry) in original.iter().zip(converted.iter()) {
            if !compare_entries(orig_entry, conv_entry) {
                return false;
            }
        }
        
        true
    }

    // Test function for round-trip validation
    pub fn test_round_trip_with_file(filename: &str) -> Result<(), String> {
        let shred_bytes = load_test_shred(filename);
        
        let original_entries = deserialize_with_solana_crates(&shred_bytes);
        let parsed_entry = deserialize_with_our_decoder(&shred_bytes);
        let converted_entries = convert_parsed_entry_to_solana_entry(&parsed_entry)?;
        
        if compare_entry_vectors(&original_entries, &converted_entries) {
            Ok(())
        } else {
            Err(format!("Round-trip validation failed for {}", filename))
        }
    }
}
