#![deny(clippy::all)]

use napi::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sult, <PERSON>};
use solana_entry::entry::Entry;

#[macro_use]
extern crate napi_derive;

pub mod types;
pub mod utils;

#[cfg(test)]
mod lib_test;

use types::ParsedEntry;
use utils::convert_entries_to_parsed;

#[napi]
pub fn decode_entries(bytes: Js<PERSON>uff<PERSON>) -> Result<ParsedEntry> {
  let entries: Vec<Entry> = bincode::deserialize(&bytes.into_value()?)
    .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))?;

  Ok(convert_entries_to_parsed(entries))
}

#[cfg(test)]
mod tests {
  use crate::types::{Hash, Pubkey, Signature};

  #[test]
  fn test_individual_type_conversions() {
    // Test Hash conversion
    let original_hash = solana_hash::Hash::new_unique();
    let wrapper_hash = Hash::from_solana_hash(&original_hash);
    let converted_hash = wrapper_hash
      .to_solana_hash()
      .expect("Hash conversion failed");
    assert_eq!(original_hash.to_bytes(), converted_hash.to_bytes());

    // Test Pubkey conversion
    let original_pubkey = solana_pubkey::Pubkey::new_unique();
    let wrapper_pubkey = Pubkey::from_solana_pubkey(&original_pubkey);
    let converted_pubkey = wrapper_pubkey
      .to_solana_pubkey()
      .expect("Pubkey conversion failed");
    assert_eq!(original_pubkey.to_bytes(), converted_pubkey.to_bytes());

    // Test Signature conversion
    let signature_bytes = [1u8; 64];
    let original_signature = solana_signature::Signature::from(signature_bytes);
    let wrapper_signature = Signature::from_solana_signature(&original_signature);
    let converted_signature = wrapper_signature
      .to_solana_signature()
      .expect("Signature conversion failed");
    assert_eq!(original_signature.as_ref(), converted_signature.as_ref());
  }

  #[test]
  fn test_error_handling_invalid_lengths() {
    // Test Hash with invalid length
    let invalid_hash = Hash::new(vec![0u8; 31]); // Should be 32 bytes
    assert!(invalid_hash.to_solana_hash().is_err());

    // Test Pubkey with invalid length
    let invalid_pubkey = Pubkey::new(vec![0u8; 31]); // Should be 32 bytes
    assert!(invalid_pubkey.to_solana_pubkey().is_err());

    // Test Signature with invalid length
    let invalid_signature = Signature::new(vec![0u8; 63]); // Should be 64 bytes
    assert!(invalid_signature.to_solana_signature().is_err());
  }
}
