use crate::types::{transactions::VersionedTransaction, Hash};
use napi_derive::napi;

#[napi(object)]
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct Entry {
  pub num_hashes: f64,
  pub hash: Hash,
  pub transactions: Vec<VersionedTransaction>,
}

impl Entry {
  pub fn from_solana_entry(entry: &solana_entry::entry::Entry) -> Self {
    Self {
      num_hashes: entry.num_hashes as f64,
      hash: Hash::from_solana_hash(&entry.hash),
      transactions: entry
        .transactions
        .iter()
        .map(|tx| VersionedTransaction::from_solana_versioned_transaction(tx))
        .collect(),
    }
  }

  pub fn to_solana_entry(&self) -> Result<solana_entry::entry::Entry, String> {
    let transactions: Result<Vec<_>, _> = self
      .transactions
      .iter()
      .map(|tx| tx.to_solana_versioned_transaction())
      .collect();

    Ok(solana_entry::entry::Entry {
      num_hashes: self.num_hashes as u64,
      hash: self.hash.to_solana_hash()?,
      transactions: transactions?,
    })
  }
}

#[napi(object)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ParsedEntry {
  pub entries: Vec<Entry>,
}
