mod helpers;

use helpers::*;
use solana_entry::entry::Entry as SolanaEntry;
use std::fs;

// Import from the local crate - only the types and utils, not NAPI functions
use shredstream_decoder::types::ParsedEntry;
use shredstream_decoder::utils::{convert_entries_to_parsed, convert_parsed_entry_to_solana_entry};

fn deserialize_with_our_decoder(bytes: &[u8]) -> ParsedEntry {
  let entries: Vec<SolanaEntry> =
    bincode::deserialize(bytes).expect("Failed to deserialize with bincode");
  convert_entries_to_parsed(entries)
}

#[test]
fn test_parsed_entry_round_trip_basic() {
  let shred_bytes = load_test_shred("shred_000001.bin");

  let original_entries = deserialize_with_solana_crates(&shred_bytes);

  let parsed_entry = deserialize_with_our_decoder(&shred_bytes);

  let converted_entries = convert_parsed_entry_to_solana_entry(&parsed_entry)
    .expect("Failed to convert ParsedEntry back to Solana entries");

  assert!(
    compare_entry_vectors(&original_entries, &converted_entries),
    "Round-trip conversion failed: original and converted entries do not match"
  );
}

#[test]
fn test_parsed_entry_round_trip_multiple_files() {
  let test_files = [
    "shred_000001.bin",
    "shred_000002.bin",
    "shred_000003.bin",
    "shred_000004.bin",
    "shred_000005.bin",
  ];

  for filename in &test_files {
    println!("Testing file: {}", filename);

    let shred_bytes = load_test_shred(filename);

    let original_entries = deserialize_with_solana_crates(&shred_bytes);

    let parsed_entry = deserialize_with_our_decoder(&shred_bytes);

    let converted_entries = convert_parsed_entry_to_solana_entry(&parsed_entry).expect(&format!(
      "Failed to convert ParsedEntry back to Solana entries for {}",
      filename
    ));

    assert!(
      compare_entry_vectors(&original_entries, &converted_entries),
      "Round-trip conversion failed for {}: original and converted entries do not match",
      filename
    );
  }
}

#[test]
fn test_parsed_entry_structure_integrity() {
  let shred_bytes = load_test_shred("shred_000001.bin");

  let original_entries = deserialize_with_solana_crates(&shred_bytes);
  let parsed_entry = deserialize_with_our_decoder(&shred_bytes);

  assert_eq!(
    original_entries.len(),
    parsed_entry.entries.len(),
    "Number of entries should match"
  );

  for (i, (original, parsed)) in original_entries
    .iter()
    .zip(parsed_entry.entries.iter())
    .enumerate()
  {
    assert_eq!(
      original.num_hashes, parsed.num_hashes as u64,
      "Entry {} num_hashes should match",
      i
    );

    assert_eq!(
      original.hash.to_bytes().to_vec(),
      parsed.hash.bytes,
      "Entry {} hash should match",
      i
    );

    assert_eq!(
      original.transactions.len(),
      parsed.transactions.len(),
      "Entry {} transaction count should match",
      i
    );
  }
}

#[test]
fn test_error_handling_empty_data() {
  let empty_bytes = vec![];

  let result = std::panic::catch_unwind(|| deserialize_with_solana_crates(&empty_bytes));

  assert!(result.is_err(), "Should fail on empty data");
}

#[test]
fn test_error_handling_invalid_data() {
  let invalid_bytes = vec![0xFF; 100];

  let result = std::panic::catch_unwind(|| deserialize_with_solana_crates(&invalid_bytes));

  assert!(result.is_err(), "Should fail on invalid data");
}

#[test]
fn test_comprehensive_round_trip_validation() {
  let test_dir = std::path::Path::new("tests/data");
  let mut test_count = 0;
  let max_tests = 10; // Limit for CI performance

  if let Ok(entries) = fs::read_dir(test_dir) {
    for entry in entries {
      if test_count >= max_tests {
        break;
      }

      if let Ok(entry) = entry {
        let path = entry.path();
        if path.extension().and_then(|s| s.to_str()) == Some("bin") {
          let filename = path.file_name().unwrap().to_str().unwrap();
          println!("Comprehensive test for: {}", filename);

          let shred_bytes = load_test_shred(filename);

          if let Ok(original_entries) =
            std::panic::catch_unwind(|| deserialize_with_solana_crates(&shred_bytes))
          {
            let parsed_entry = deserialize_with_our_decoder(&shred_bytes);

            if let Ok(converted_entries) = convert_parsed_entry_to_solana_entry(&parsed_entry) {
              assert!(
                compare_entry_vectors(&original_entries, &converted_entries),
                "Comprehensive round-trip failed for {}",
                filename
              );
              test_count += 1;
            }
          }
        }
      }
    }
  }

  assert!(test_count > 0, "No test files were processed");
  println!("Successfully tested {} files", test_count);
}

#[test]
fn test_performance_benchmark() {
  let shred_bytes = load_test_shred("shred_000001.bin");

  let start = std::time::Instant::now();
  for _ in 0..100 {
    let _original_entries = deserialize_with_solana_crates(&shred_bytes);
  }
  let solana_time = start.elapsed();

  let start = std::time::Instant::now();
  for _ in 0..100 {
    let _parsed_entry = deserialize_with_our_decoder(&shred_bytes);
  }
  let our_time = start.elapsed();

  println!("Solana crates time: {:?}", solana_time);
  println!("Our decoder time: {:?}", our_time);

  let overhead_ratio = our_time.as_nanos() as f64 / solana_time.as_nanos() as f64;
  println!("Overhead ratio: {:.2}x", overhead_ratio);

  assert!(
    overhead_ratio < 5.0,
    "Conversion overhead should be reasonable (< 5x)"
  );
}

#[test]
fn test_field_by_field_validation() {
  let shred_bytes = load_test_shred("shred_000001.bin");

  let original_entries = deserialize_with_solana_crates(&shred_bytes);
  let parsed_entry = deserialize_with_our_decoder(&shred_bytes);
  let converted_entries =
    convert_parsed_entry_to_solana_entry(&parsed_entry).expect("Failed to convert back");

  for (i, (original, converted)) in original_entries
    .iter()
    .zip(converted_entries.iter())
    .enumerate()
  {
    assert!(
      compare_entries(original, converted),
      "Field-by-field validation failed for entry {}",
      i
    );
  }
}
