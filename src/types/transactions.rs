use crate::types::{
  messages::{LegacyMessage, V0Message},
  Signature,
};
use napi_derive::napi;

#[napi(object)]
#[derive(Debug, Clone)]
pub struct VersionedMessage {
  pub message_type: String,
  pub legacy: Option<LegacyMessage>,
  pub v0: Option<V0Message>,
}

impl VersionedMessage {
  pub fn from_solana_versioned_message(message: &solana_message::VersionedMessage) -> Self {
    match message {
      solana_message::VersionedMessage::Legacy(legacy_msg) => Self {
        message_type: "Legacy".to_string(),
        legacy: Some(LegacyMessage::from_solana_message(legacy_msg)),
        v0: None,
      },
      solana_message::VersionedMessage::V0(v0_msg) => Self {
        message_type: "V0".to_string(),
        legacy: None,
        v0: Some(V0Message::from_solana_v0_message(v0_msg)),
      },
    }
  }

  pub fn to_solana_versioned_message(&self) -> Result<solana_message::VersionedMessage, String> {
    match self.message_type.as_str() {
      "Legacy" => {
        if let Some(ref legacy) = self.legacy {
          Ok(solana_message::VersionedMessage::Legacy(
            legacy.to_solana_message()?,
          ))
        } else {
          Err("Legacy message type specified but legacy field is None".to_string())
        }
      }
      "V0" => {
        if let Some(ref v0) = self.v0 {
          Ok(solana_message::VersionedMessage::V0(
            v0.to_solana_v0_message()?,
          ))
        } else {
          Err("V0 message type specified but v0 field is None".to_string())
        }
      }
      _ => Err(format!("Unknown message type: {}", self.message_type)),
    }
  }
}

#[napi(object)]
#[derive(Debug, Clone)]
pub struct VersionedTransaction {
  pub signatures: Vec<Signature>,
  pub message: VersionedMessage,
}

impl VersionedTransaction {
  pub fn from_solana_versioned_transaction(
    transaction: &solana_transaction::versioned::VersionedTransaction,
  ) -> Self {
    Self {
      signatures: transaction
        .signatures
        .iter()
        .map(|sig| Signature::from_solana_signature(sig))
        .collect(),
      message: VersionedMessage::from_solana_versioned_message(&transaction.message),
    }
  }

  pub fn to_solana_versioned_transaction(
    &self,
  ) -> Result<solana_transaction::versioned::VersionedTransaction, String> {
    let signatures: Result<Vec<_>, _> = self
      .signatures
      .iter()
      .map(|sig| sig.to_solana_signature())
      .collect();

    Ok(solana_transaction::versioned::VersionedTransaction {
      signatures: signatures?,
      message: self.message.to_solana_versioned_message()?,
    })
  }
}
