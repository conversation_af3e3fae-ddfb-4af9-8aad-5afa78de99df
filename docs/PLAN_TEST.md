# Integration Test Plan for NAPI Shredstream Decoder

## Project Context

### Overview

This is a Node.js addon using NAPI to create Rust helper functions for decoding Jito shredstream data and exposing them to TypeScript. The project wraps Solana blockchain data structures for TypeScript interoperability.

### Current Architecture

```
src/
├── lib.rs                    # Main NAPI entry point with decode_entries()
├── types/                    # TypeScript-compatible wrapper structs
│   ├── entries.rs           # Entry, ParsedEntry
│   ├── hashes.rs            # Hash
│   ├── pubkeys.rs           # Pubkey
│   ├── signatures.rs        # Signature
│   ├── messages.rs          # MessageHeader, CompiledInstruction, etc.
│   └── transactions.rs      # VersionedTransaction, VersionedMessage
└── utils/
    └── conversions.rs       # convert_entries_to_parsed()
```

### Data Flow

1. Raw shred bytes → `decode_entries(bytes)` → `ParsedEntry` for TypeScript
2. Uses `bincode::deserialize()` to get `Vec<solana_entry::entry::Entry>`
3. Converts via `convert_entries_to_parsed()` using `from_solana_*` methods

### Wrapper Structs Inventory

**Core Types:**

-   `Hash` (wraps `solana_hash::Hash`)
-   `Pubkey` (wraps `solana_pubkey::Pubkey`)
-   `Signature` (wraps `solana_signature::Signature`)

**Message Types:**

-   `MessageHeader` (wraps `solana_message::MessageHeader`)
-   `CompiledInstruction` (wraps `solana_message::compiled_instruction::CompiledInstruction`)
-   `MessageAddressTableLookup` (wraps `solana_message::v0::MessageAddressTableLookup`)
-   `LegacyMessage` (wraps `solana_message::Message`)
-   `V0Message` (wraps `solana_message::v0::Message`)

**Transaction Types:**

-   `VersionedMessage` (wraps `solana_message::VersionedMessage`)
-   `VersionedTransaction` (wraps `solana_transaction::versioned::VersionedTransaction`)

**Entry Types:**

-   `Entry` (wraps `solana_entry::entry::Entry`)
-   `ParsedEntry` (container for `Vec<Entry>`)

### Test Data Available

-   Real shred data in `tests/data/shred_*.bin` (222 files collected from Jito Shredstream)
-   File sizes: 3KB - 54KB
-   Slots: 344333101 - 344333130
-   Commitment level: Confirmed

## Test Strategy

### Methodology: Simplified Round-Trip Validation

Focus on testing only the top-level `ParsedEntry` struct, which encompasses all nested wrapper structs:

**Test Flow:**

1. **Deserialize with Solana crates**: Use `bincode::deserialize()` directly with Solana crates to get canonical `Vec<solana_entry::entry::Entry>`
2. **Deserialize with our decoder**: Use `decode_entries()` function to get `ParsedEntry`
3. **Convert back to Solana**: Use new `convert_parsed_entry_to_solana_entry()` to convert `ParsedEntry` → `Vec<solana_entry::entry::Entry>`
4. **Field-by-Field Comparison**: Compare original Solana structs with converted structs to verify 100% data integrity
5. **Edge Case Testing**: Handle malformed/empty data

### Rationale for Simplified Approach

Since `ParsedEntry` contains all other wrapper structs as nested fields, testing this single struct comprehensively validates:

-   All primitive types (`Hash`, `Pubkey`, `Signature`)
-   All message components (`MessageHeader`, `CompiledInstruction`, etc.)
-   All transaction structures (`VersionedTransaction`, `VersionedMessage`)
-   Complete data flow from raw shreds to TypeScript-compatible format

This approach prioritizes simplicity while maintaining 100% coverage of all conversion logic.

### Test Focus

**Target Struct:** `ParsedEntry` only
**Test Methodology:** End-to-end round-trip validation with real shred data
**Implementation:** Single comprehensive test suite covering all nested structures

## Implementation Plan

### Phase 1: Foundation Setup

**Files to Create:**

-   `tests/integration_test.rs` - Main test file
-   `tests/helpers/mod.rs` - Test helper functions

**Key Function to Implement:**

```rust
// In src/utils/conversions.rs (add to existing file)
pub fn convert_parsed_entry_to_solana_entry(parsed: &ParsedEntry) -> Vec<solana_entry::entry::Entry>
```

### Phase 2: Test Infrastructure

**Test Helper Functions:**

```rust
// In tests/helpers/mod.rs
pub fn load_test_shred(filename: &str) -> Vec<u8>
pub fn deserialize_with_solana_crates(bytes: &[u8]) -> Vec<solana_entry::entry::Entry>
pub fn deserialize_with_our_decoder(bytes: &[u8]) -> ParsedEntry
pub fn compare_entries(original: &solana_entry::entry::Entry, converted: &solana_entry::entry::Entry) -> bool
pub fn compare_entry_vectors(original: &[solana_entry::entry::Entry], converted: &[solana_entry::entry::Entry]) -> bool
```

### Phase 3: Implementation

**Test Implementation Focus:**

1. Implement `convert_parsed_entry_to_solana_entry()` function
2. Create comprehensive round-trip test for `ParsedEntry`
3. Test with real shred data from `tests/data/`
4. Validate 100% data integrity across all nested structures

**Detailed Test Implementation:**

```rust
// Example test structure in tests/integration_test.rs
#[test]
fn test_parsed_entry_round_trip() {
    // Step 1: Load real shred data
    let shred_bytes = load_test_shred("shred_000001.bin");

    // Step 2: Deserialize with Solana crates (canonical reference)
    let original_entries = deserialize_with_solana_crates(&shred_bytes);

    // Step 3: Deserialize with our decoder
    let parsed_entry = deserialize_with_our_decoder(&shred_bytes);

    // Step 4: Convert back to Solana structs
    let converted_entries = convert_parsed_entry_to_solana_entry(&parsed_entry);

    // Step 5: Field-by-field comparison
    assert!(compare_entry_vectors(&original_entries, &converted_entries));
}
```

## Progress Tracking

### Completion Checklist

#### ✅ Infrastructure Setup

-   [ ] Create `tests/integration_test.rs`
-   [ ] Create `tests/helpers/mod.rs`
-   [ ] Add test dependencies to `Cargo.toml`

#### ✅ Core Function Implementation

-   [ ] Implement `convert_parsed_entry_to_solana_entry()` in `src/utils/conversions.rs`

#### ✅ ParsedEntry Round-Trip Tests

-   [ ] Basic ParsedEntry round-trip test
-   [ ] Test with real shred data (single file)
-   [ ] Test with multiple shred files
-   [ ] Field-by-field validation for all nested structures

#### ✅ Integration Tests (Real Data)

-   [ ] Test decode_entries() with real data
-   [ ] Validate ParsedEntry structure integrity
-   [ ] Performance benchmarks
-   [ ] Error handling tests

#### ✅ Coverage Analysis

-   [ ] Achieve 100% line coverage
-   [ ] Achieve 100% branch coverage
-   [ ] Document any uncoverable code
-   [ ] Generate coverage report

### Success Criteria

1. **100% Round-Trip Accuracy**: All data survives Solana crates → ParsedEntry → Solana crates conversion with perfect field-by-field matching
2. **Real Data Compatibility**: Successfully processes all 222 test shred files from `tests/data/`
3. **Complete Coverage**: Tests validate all nested structures within ParsedEntry (Hash, Pubkey, Signature, Messages, Transactions)
4. **Performance Validation**: Conversion overhead is minimal compared to direct Solana crate deserialization
5. **Error Resilience**: Graceful handling of malformed data with proper error messages

## Technical Specifications

### Test Dependencies

```toml
[dev-dependencies]
solana-entry = "2.2.7"
solana-transaction = "2.2.2"
solana-message = "2.2.1"
solana-pubkey = "2.2.1"
solana-hash = "2.2.1"
solana-signature = "2.2.1"
```

### Test Data Management

-   Use `tests/data/shred_*.bin` files
-   Implement lazy loading for large datasets
-   Cache parsed results for performance
-   Test with subset for CI, full set for comprehensive validation

### Error Scenarios to Test

-   Empty byte arrays
-   Invalid signatures
-   Malformed messages
-   Oversized data
-   Network byte order issues
-   Unicode handling in strings

## AI Assistant Guidelines

### Context Preservation

This document serves as the single source of truth for the integration testing effort. Any AI assistant working on this project should:

1. **Read this document first** to understand current progress
2. **Update progress checkboxes** as work is completed
3. **Add new findings** to relevant sections
4. **Maintain consistency** with established patterns
5. **Document any deviations** from the plan

### Handoff Protocol

When stopping work, update:

-   [ ] Progress checkboxes with current status
-   [ ] Any new issues discovered
-   [ ] Next recommended steps
-   [ ] Code quality observations

### Code Quality Standards

-   Follow existing project patterns
-   Use descriptive test names
-   Include comprehensive error messages
-   Add inline documentation for complex logic
-   Maintain consistent formatting

---

**Last Updated:** Initial creation
**Next Phase:** Infrastructure Setup
**Estimated Completion:** TBD based on implementation progress
