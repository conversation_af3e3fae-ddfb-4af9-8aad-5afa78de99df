use napi_derive::napi;

#[napi(object)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Pubkey {
  pub bytes: Vec<u8>,
}

impl Pubkey {
  pub fn new(bytes: Vec<u8>) -> Self {
    Self { bytes }
  }

  pub fn from_solana_pubkey(pubkey: &solana_pubkey::Pubkey) -> Self {
    Self {
      bytes: pubkey.to_bytes().to_vec(),
    }
  }

  pub fn to_solana_pubkey(&self) -> Result<solana_pubkey::Pubkey, String> {
    if self.bytes.len() != 32 {
      return Err(format!(
        "Invalid pubkey length: expected 32 bytes, got {}",
        self.bytes.len()
      ));
    }
    let mut array = [0u8; 32];
    array.copy_from_slice(&self.bytes);
    Ok(solana_pubkey::Pubkey::new_from_array(array))
  }
}
