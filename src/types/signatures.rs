use napi_derive::napi;

#[napi(object)]
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Signature {
  pub bytes: Vec<u8>,
}

impl Signature {
  pub fn new(bytes: Vec<u8>) -> Self {
    Self { bytes }
  }

  pub fn from_solana_signature(signature: &solana_signature::Signature) -> Self {
    Self {
      bytes: signature.as_ref().to_vec(),
    }
  }

  pub fn to_solana_signature(&self) -> Result<solana_signature::Signature, String> {
    if self.bytes.len() != 64 {
      return Err(format!(
        "Invalid signature length: expected 64 bytes, got {}",
        self.bytes.len()
      ));
    }
    let mut array = [0u8; 64];
    array.copy_from_slice(&self.bytes);
    Ok(solana_signature::Signature::from(array))
  }
}
