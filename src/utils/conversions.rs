use crate::types::{Entry, ParsedEntry};

pub fn convert_entries_to_parsed(entries: Vec<solana_entry::entry::Entry>) -> ParsedEntry {
  ParsedEntry {
    entries: entries
      .iter()
      .map(|entry| Entry::from_solana_entry(entry))
      .collect(),
  }
}

pub fn convert_parsed_entry_to_solana_entry(
  parsed: &ParsedEntry,
) -> Result<Vec<solana_entry::entry::Entry>, String> {
  parsed
    .entries
    .iter()
    .map(|entry| entry.to_solana_entry())
    .collect()
}
