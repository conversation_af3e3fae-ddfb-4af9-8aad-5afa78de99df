// Test utilities and helper functions for shredstream-decoder
// Note: This file contains only non-NAPI dependent tests

// Simple tests for type conversions without NAPI dependencies
#[cfg(test)]
mod tests {
    // Test basic type conversion functionality
    #[test]
    fn test_basic_type_conversions() {
        // Test Hash conversion
        let original_hash = solana_hash::Hash::new_unique();
        let wrapper_hash = shredstream_decoder::types::Hash::from_solana_hash(&original_hash);
        let converted_hash = wrapper_hash
            .to_solana_hash()
            .expect("Hash conversion failed");
        assert_eq!(original_hash.to_bytes(), converted_hash.to_bytes());

        // Test Pubkey conversion
        let original_pubkey = solana_pubkey::Pubkey::new_unique();
        let wrapper_pubkey = shredstream_decoder::types::Pubkey::from_solana_pubkey(&original_pubkey);
        let converted_pubkey = wrapper_pubkey
            .to_solana_pubkey()
            .expect("Pubkey conversion failed");
        assert_eq!(original_pubkey.to_bytes(), converted_pubkey.to_bytes());

        // Test Signature conversion
        let signature_bytes = [1u8; 64];
        let original_signature = solana_signature::Signature::from(signature_bytes);
        let wrapper_signature = shredstream_decoder::types::Signature::from_solana_signature(&original_signature);
        let converted_signature = wrapper_signature
            .to_solana_signature()
            .expect("Signature conversion failed");
        assert_eq!(original_signature.as_ref(), converted_signature.as_ref());
    }

    #[test]
    fn test_error_handling_invalid_lengths() {
        // Test Hash with invalid length
        let invalid_hash = shredstream_decoder::types::Hash::new(vec![0u8; 31]); // Should be 32 bytes
        assert!(invalid_hash.to_solana_hash().is_err());

        // Test Pubkey with invalid length
        let invalid_pubkey = shredstream_decoder::types::Pubkey::new(vec![0u8; 31]); // Should be 32 bytes
        assert!(invalid_pubkey.to_solana_pubkey().is_err());

        // Test Signature with invalid length
        let invalid_signature = shredstream_decoder::types::Signature::new(vec![0u8; 63]); // Should be 64 bytes
        assert!(invalid_signature.to_solana_signature().is_err());
    }

    #[test]
    fn test_message_header_conversion() {
        let original_header = solana_message::MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 1,
        };
        
        let wrapper_header = shredstream_decoder::types::MessageHeader::from_solana_header(&original_header);
        let converted_header = wrapper_header.to_solana_header();
        
        assert_eq!(original_header.num_required_signatures, converted_header.num_required_signatures);
        assert_eq!(original_header.num_readonly_signed_accounts, converted_header.num_readonly_signed_accounts);
        assert_eq!(original_header.num_readonly_unsigned_accounts, converted_header.num_readonly_unsigned_accounts);
    }

    #[test]
    fn test_compiled_instruction_conversion() {
        let original_instruction = solana_message::compiled_instruction::CompiledInstruction {
            program_id_index: 1,
            accounts: vec![0, 1, 2],
            data: vec![1, 2, 3, 4],
        };
        
        let wrapper_instruction = shredstream_decoder::types::CompiledInstruction::from_solana_instruction(&original_instruction);
        let converted_instruction = wrapper_instruction.to_solana_instruction();
        
        assert_eq!(original_instruction.program_id_index, converted_instruction.program_id_index);
        assert_eq!(original_instruction.accounts, converted_instruction.accounts);
        assert_eq!(original_instruction.data, converted_instruction.data);
    }

    #[test]
    fn test_conversion_utils_exist() {
        // Test that conversion utility functions exist and can be called
        // This is a basic smoke test to ensure the functions are properly exported
        
        // Create a simple Entry for testing
        let entry = solana_entry::entry::Entry {
            num_hashes: 1,
            hash: solana_hash::Hash::new_unique(),
            transactions: vec![],
        };
        
        // Test convert_entries_to_parsed
        let parsed = shredstream_decoder::utils::convert_entries_to_parsed(vec![entry.clone()]);
        assert_eq!(parsed.entries.len(), 1);
        assert_eq!(parsed.entries[0].num_hashes, 1.0);
        
        // Test convert_parsed_entry_to_solana_entry
        let converted = shredstream_decoder::utils::convert_parsed_entry_to_solana_entry(&parsed)
            .expect("Conversion should succeed");
        assert_eq!(converted.len(), 1);
        assert_eq!(converted[0].num_hashes, entry.num_hashes);
    }

    #[test]
    fn test_address_table_lookup_conversion() {
        let account_key = solana_pubkey::Pubkey::new_unique();
        let original_lookup = solana_message::v0::MessageAddressTableLookup {
            account_key,
            writable_indexes: vec![0, 1],
            readonly_indexes: vec![2, 3],
        };
        
        let wrapper_lookup = shredstream_decoder::types::MessageAddressTableLookup::from_solana_lookup(&original_lookup);
        let converted_lookup = wrapper_lookup.to_solana_lookup().expect("Conversion should succeed");
        
        assert_eq!(original_lookup.account_key.to_bytes(), converted_lookup.account_key.to_bytes());
        assert_eq!(original_lookup.writable_indexes, converted_lookup.writable_indexes);
        assert_eq!(original_lookup.readonly_indexes, converted_lookup.readonly_indexes);
    }

    #[test]
    fn test_legacy_message_conversion() {
        let header = solana_message::MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 1,
        };
        
        let account_keys = vec![solana_pubkey::Pubkey::new_unique()];
        let recent_blockhash = solana_hash::Hash::new_unique();
        let instructions = vec![
            solana_message::compiled_instruction::CompiledInstruction {
                program_id_index: 0,
                accounts: vec![],
                data: vec![],
            }
        ];
        
        let original_message = solana_message::Message {
            header,
            account_keys,
            recent_blockhash,
            instructions,
        };
        
        let wrapper_message = shredstream_decoder::types::LegacyMessage::from_solana_message(&original_message);
        let converted_message = wrapper_message.to_solana_message().expect("Conversion should succeed");
        
        assert_eq!(original_message.header.num_required_signatures, converted_message.header.num_required_signatures);
        assert_eq!(original_message.account_keys.len(), converted_message.account_keys.len());
        assert_eq!(original_message.recent_blockhash.to_bytes(), converted_message.recent_blockhash.to_bytes());
        assert_eq!(original_message.instructions.len(), converted_message.instructions.len());
    }

    #[test]
    fn test_versioned_message_conversion() {
        let header = solana_message::MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 1,
        };
        
        let account_keys = vec![solana_pubkey::Pubkey::new_unique()];
        let recent_blockhash = solana_hash::Hash::new_unique();
        let instructions = vec![];
        
        let legacy_message = solana_message::Message {
            header,
            account_keys,
            recent_blockhash,
            instructions,
        };
        
        let original_versioned = solana_message::VersionedMessage::Legacy(legacy_message);
        
        let wrapper_versioned = shredstream_decoder::types::VersionedMessage::from_solana_versioned_message(&original_versioned);
        let converted_versioned = wrapper_versioned.to_solana_versioned_message().expect("Conversion should succeed");
        
        match (&original_versioned, &converted_versioned) {
            (solana_message::VersionedMessage::Legacy(orig), solana_message::VersionedMessage::Legacy(conv)) => {
                assert_eq!(orig.header.num_required_signatures, conv.header.num_required_signatures);
                assert_eq!(orig.account_keys.len(), conv.account_keys.len());
            }
            _ => panic!("Message types should match"),
        }
    }

    #[test]
    fn test_versioned_transaction_conversion() {
        let signatures = vec![solana_signature::Signature::from([1u8; 64])];
        
        let header = solana_message::MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 0,
        };
        
        let message = solana_message::Message {
            header,
            account_keys: vec![],
            recent_blockhash: solana_hash::Hash::new_unique(),
            instructions: vec![],
        };
        
        let original_transaction = solana_transaction::versioned::VersionedTransaction {
            signatures,
            message: solana_message::VersionedMessage::Legacy(message),
        };
        
        let wrapper_transaction = shredstream_decoder::types::VersionedTransaction::from_solana_versioned_transaction(&original_transaction);
        let converted_transaction = wrapper_transaction.to_solana_versioned_transaction().expect("Conversion should succeed");
        
        assert_eq!(original_transaction.signatures.len(), converted_transaction.signatures.len());
        assert_eq!(original_transaction.signatures[0].as_ref(), converted_transaction.signatures[0].as_ref());
    }

    #[test]
    fn test_entry_conversion() {
        let hash = solana_hash::Hash::new_unique();
        let transaction = solana_transaction::versioned::VersionedTransaction {
            signatures: vec![],
            message: solana_message::VersionedMessage::Legacy(solana_message::Message {
                header: solana_message::MessageHeader {
                    num_required_signatures: 0,
                    num_readonly_signed_accounts: 0,
                    num_readonly_unsigned_accounts: 0,
                },
                account_keys: vec![],
                recent_blockhash: solana_hash::Hash::new_unique(),
                instructions: vec![],
            }),
        };
        
        let original_entry = solana_entry::entry::Entry {
            num_hashes: 42,
            hash,
            transactions: vec![transaction],
        };
        
        let wrapper_entry = shredstream_decoder::types::Entry::from_solana_entry(&original_entry);
        let converted_entry = wrapper_entry.to_solana_entry().expect("Conversion should succeed");
        
        assert_eq!(original_entry.num_hashes, converted_entry.num_hashes);
        assert_eq!(original_entry.hash.to_bytes(), converted_entry.hash.to_bytes());
        assert_eq!(original_entry.transactions.len(), converted_entry.transactions.len());
    }
}
